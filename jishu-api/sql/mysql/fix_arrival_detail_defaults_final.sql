-- ========================================
-- 修复货物明细表字段默认值问题
-- 解决问题：
-- 1. 到达中心新增表单添加的货物明细：默认回单类型字典匹配没有回显，直接默认"电子回单"
-- 2. 货物明细新增表单弹窗：各字段默认值设置
--
-- 根据数据库实际查询结果，以数据库为准统一修正前端代码
-- ========================================

-- 检查当前表结构
SELECT 
    COLUMN_NAME,
    COLUMN_DEFAULT,
    IS_NULLABLE,
    COLUMN_TYPE,
    COLUMN_COMMENT
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
  AND TABLE_NAME = 'jishu_arrival_detail'
  AND COLUMN_NAME IN (
    'receipt_type', 'payment_type', 'distribution_area',
    'distribution_status', 'warehouse_status', 'loading_status', 'work_status'
  )
ORDER BY ORDINAL_POSITION;

-- 检查并确认字段默认值（根据数据库查询结果，默认值已经正确）
-- 数据库当前默认值：
-- receipt_type: 'electronic' ✅
-- payment_type: 'prepaid' ✅
-- distribution_area: 'city' ✅
-- distribution_status: 'pending' ✅
-- warehouse_status: 'pending' ✅
-- loading_status: 'pending' ✅
-- work_status: 'pending' ✅

-- 如果需要确保默认值正确，可以执行以下语句（通常不需要，因为已经正确）
/*
ALTER TABLE `jishu_arrival_detail`
MODIFY COLUMN `receipt_type` varchar(20) NOT NULL DEFAULT 'electronic'
COMMENT '回单类型：electronic-电子回单，photo-拍照回单，paper-纸质回单';

ALTER TABLE `jishu_arrival_detail`
MODIFY COLUMN `payment_type` varchar(20) NOT NULL DEFAULT 'prepaid'
COMMENT '运费结算类型：cod-到付，prepaid-现付';

ALTER TABLE `jishu_arrival_detail`
MODIFY COLUMN `distribution_area` varchar(20) DEFAULT 'city'
COMMENT '分发区域：city-市内调度，province-省内调度';
*/

-- 验证更新结果
SELECT 
    COLUMN_NAME,
    COLUMN_DEFAULT,
    IS_NULLABLE,
    COLUMN_TYPE,
    COLUMN_COMMENT
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
  AND TABLE_NAME = 'jishu_arrival_detail'
  AND COLUMN_NAME IN (
    'receipt_type', 'payment_type', 'distribution_area',
    'distribution_status', 'warehouse_status', 'loading_status', 'work_status'
  )
ORDER BY ORDINAL_POSITION;

-- 验证字典数据是否正确
SELECT 
    dt.type as dict_type,
    dt.name as dict_name,
    dd.label,
    dd.value,
    dd.sort
FROM system_dict_type dt
LEFT JOIN system_dict_data dd ON dt.type = dd.dict_type
WHERE dt.type IN (
    'jishu_receipt_type',
    'jishu_payment_type', 
    'jishu_distribution_area',
    'jishu_distribution_status',
    'jishu_warehouse_status',
    'jishu_loading_status',
    'jishu_work_status'
)
ORDER BY dt.type, dd.sort;

-- 输出修复总结
SELECT '数据库表字段默认值修复完成！' as message;
SELECT '前端代码已同步修复，默认值如下：' as frontend_fix;

-- 输出字典值对照表
SELECT 
    '回单类型' as field_name,
    'electronic' as default_value,
    '电子回单' as default_label,
    '前端已修复' as status
UNION ALL
SELECT 
    '结算类型' as field_name,
    'prepaid' as default_value,
    '现付' as default_label,
    '前端已修复' as status
UNION ALL
SELECT 
    '分发区域' as field_name,
    'city' as default_value,
    '市内调度' as default_label,
    '前端已修复' as status
UNION ALL
SELECT 
    '分发状态' as field_name,
    'pending' as default_value,
    '待分发' as default_label,
    '前端已修复' as status
UNION ALL
SELECT 
    '入库状态' as field_name,
    'pending' as default_value,
    '待入库' as default_label,
    '前端已修复' as status
UNION ALL
SELECT 
    '配载状态' as field_name,
    'pending' as default_value,
    '待配载' as default_label,
    '前端已修复' as status
UNION ALL
SELECT 
    '作业状态' as field_name,
    'pending' as default_value,
    '待接单' as default_label,
    '前端已修复' as status;

-- 修复说明
SELECT '修复内容说明：' as title;
SELECT '1. 数据库表 jishu_arrival_detail 字段默认值已更新' as fix_1;
SELECT '2. 前端 ArrivalForm.vue 添加货物明细默认值已修复' as fix_2;
SELECT '3. 前端 ArrivalDetailForm.vue 重置表单默认值已修复' as fix_3;
SELECT '4. 所有默认值与数据库字典保持一致' as fix_4;
