# 货物明细默认值修复总结

## 问题描述

1. **到达中心新增表单添加的货物明细**：默认回单类型字典匹配没有回显，直接默认"电子回单"
2. **货物明细新增表单弹窗**：各字段需要正确的默认值

## 数据库查询结果

通过查询数据库，确认了以下信息：

### 字典数据 (system_dict_data)
```
+---------------------------+--------------------+--------------+-----------------+------+
| dict_type                 | dict_name          | label        | value           | sort |
+---------------------------+--------------------+--------------+-----------------+------+
| jishu_distribution_area   | 分发区域           | 市内调度     | city            |    1 |
| jishu_distribution_area   | 分发区域           | 省内调度     | province        |    2 |
| jishu_distribution_status | 分发状态           | 待分发       | pending         |    1 |
| jishu_distribution_status | 分发状态           | 预分发       | pre_distributed |    2 |
| jishu_distribution_status | 分发状态           | 已分发       | distributed     |    3 |
| jishu_loading_status      | 配载状态           | 待配载       | pending         |    1 |
| jishu_loading_status      | 配载状态           | 已配载       | loaded          |    2 |
| jishu_payment_type        | 运费结算类型       | 到付         | cod             |    1 |
| jishu_payment_type        | 运费结算类型       | 现付         | prepaid         |    2 |
| jishu_receipt_type        | 回单类型           | 电子回单     | electronic      |    1 |
| jishu_receipt_type        | 回单类型           | 拍照回单     | photo           |    2 |
| jishu_receipt_type        | 回单类型           | 纸质回单     | paper           |    3 |
| jishu_warehouse_status    | 入出库状态         | 待入库       | pending         |    1 |
| jishu_warehouse_status    | 入出库状态         | 已入库       | in_stock        |    2 |
| jishu_warehouse_status    | 入出库状态         | 已出库       | out_stock       |    3 |
| jishu_work_status         | 作业状态           | 待接单       | pending         |    1 |
| jishu_work_status         | 作业状态           | 司机接单     | accepted        |    2 |
| jishu_work_status         | 作业状态           | 货物装车     | loading         |    3 |
| jishu_work_status         | 作业状态           | 开始运输     | transporting    |    4 |
| jishu_work_status         | 作业状态           | 货物签收     | delivered       |    5 |
| jishu_work_status         | 作业状态           | 回单签署     | receipt_signed  |    6 |
+---------------------------+--------------------+--------------+-----------------+------+
```

### 数据库表默认值 (jishu_arrival_detail)
```
+---------------------+----------------+-------------+-------------+
| COLUMN_NAME         | COLUMN_DEFAULT | IS_NULLABLE | COLUMN_TYPE |
+---------------------+----------------+-------------+-------------+
| receipt_type        | electronic     | NO          | varchar(20) |
| payment_type        | prepaid        | NO          | varchar(20) |
| distribution_status | pending        | NO          | varchar(20) |
| warehouse_status    | pending        | NO          | varchar(20) |
| loading_status      | pending        | NO          | varchar(20) |
| work_status         | pending        | NO          | varchar(20) |
| distribution_area   | city           | YES         | varchar(20) |
+---------------------+----------------+-------------+-------------+
```

## 修复内容

### 1. 数据库状态
✅ **数据库表默认值已经正确**，无需修改

### 2. 前端代码修复

#### 2.1 ArrivalForm.vue (到达中心表单)
**文件**: `jishu-admin/src/views/jishu/arrival/ArrivalForm.vue`

✅ **已修复** - 添加货物明细和重置表单时的默认值：
```javascript
receiptType: 'electronic',     // 默认电子回单
paymentType: 'prepaid',        // 默认现付
distributionArea: 'city',      // 默认市内调度
distributionStatus: 'pending', // 默认待分发
warehouseStatus: 'pending',    // 默认待入库
loadingStatus: 'pending',      // 默认未配载
workStatus: 'pending',         // 默认待接单
```

#### 2.2 ArrivalDetailForm.vue (货物明细表单)
**文件**: `jishu-admin/src/views/jishu/arrivalDetail/ArrivalDetailForm.vue`

✅ **已修复** - 初始化表单数据和重置表单时的默认值：
```javascript
// 修复前：所有字段都是 undefined
receiptType: undefined,
paymentType: undefined,
// ...

// 修复后：设置正确的默认值
receiptType: 'electronic',     // 默认电子回单
paymentType: 'prepaid',        // 默认现付
distributionStatus: 'pending', // 默认待分发
warehouseStatus: 'pending',    // 默认待入库
loadingStatus: 'pending',      // 默认未配载
workStatus: 'pending',         // 默认待接单
distributionArea: 'city',      // 默认市内调度
```

## 字典值对照表

| 字段中文名 | 字段英文名 | 字典类型 | 默认值 | 默认标签 |
|-----------|-----------|----------|--------|----------|
| 回单类型 | receipt_type | jishu_receipt_type | electronic | 电子回单 |
| 结算类型 | payment_type | jishu_payment_type | prepaid | 现付 |
| 分发区域 | distribution_area | jishu_distribution_area | city | 市内调度 |
| 分发状态 | distribution_status | jishu_distribution_status | pending | 待分发 |
| 入库状态 | warehouse_status | jishu_warehouse_status | pending | 待入库 |
| 配载状态 | loading_status | jishu_loading_status | pending | 待配载 |
| 作业状态 | work_status | jishu_work_status | pending | 待接单 |

## 验证步骤

1. **重启前端应用**
```bash
cd jishu-admin
npm run dev-server
```

2. **测试场景**
   - 到达中心 -> 新增车次 -> 添加货物明细：检查默认值是否正确显示
   - 货物明细 -> 新增：检查表单默认值是否正确
   - 确认所有下拉框都能正确显示默认选中项

3. **预期结果**
   - 回单类型默认显示"电子回单"
   - 结算类型默认显示"现付"
   - 分发区域默认显示"市内调度"
   - 所有状态字段默认显示对应的"待xxx"状态

## 总结

✅ **修复完成**：
1. 数据库表默认值已经正确，无需修改
2. 前端代码已统一按数据库字典值设置默认值
3. 所有默认值与数据库字典保持一致
4. 解决了表单初始化时字段为 undefined 导致的显示问题

🔍 **关键修复点**：
- 修正了 ArrivalDetailForm.vue 中 formData 初始化时所有字段都是 undefined 的问题
- 确保了前端默认值与数据库字典值完全一致
- 统一了添加货物明细和重置表单时的默认值设置
