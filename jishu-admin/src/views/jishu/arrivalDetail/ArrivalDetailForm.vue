<template>
  <Dialog :title="dialogTitle" v-model="dialogVisible" width="1200px">
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="120px"
      v-loading="formLoading"
    >
      <el-row>
        <el-col :span="12">
          <el-form-item label="车次信息" prop="arrivalId">
            <el-select v-model="formData.arrivalId" placeholder="请选择车次信息" style="width: 100%">
              <el-option
                v-for="arrival in arrivalList"
                :key="arrival.id"
                :label="`${arrival.tripNo} - ${arrival.licensePlate}`"
                :value="arrival.id"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="运单号" prop="waybillNo">
            <el-input v-model="formData.waybillNo" placeholder="请输入运单号" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="系统运单号" prop="systemWaybillNo">
            <el-input v-model="formData.systemWaybillNo" placeholder="确认到车后自动生成" :disabled="true" />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="8">
          <el-form-item label="件数" prop="quantity">
            <el-input-number
              v-model="formData.quantity"
              :min="1"
              placeholder="请输入件数"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="重量(kg)" prop="weight">
            <el-input-number
              v-model="formData.weight"
              :precision="2"
              :min="0.01"
              placeholder="请输入重量"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="方数(m³)" prop="volume">
            <el-input-number
              v-model="formData.volume"
              :precision="2"
              :min="0.01"
              placeholder="请输入方数"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="12">
          <el-form-item label="体积(m³)" prop="cubicVolume">
            <el-input-number
              v-model="formData.cubicVolume"
              :precision="2"
              :min="0"
              placeholder="请输入体积"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="货物名称" prop="goodsName">
            <el-input v-model="formData.goodsName" placeholder="请输入货物名称" />
          </el-form-item>
        </el-col>
      </el-row>
      
      <!-- 发货人信息 -->
      <el-divider content-position="left">发货人信息</el-divider>
      <el-row>
        <el-col :span="8">
          <el-form-item label="发货人姓名" prop="senderName">
            <el-input v-model="formData.senderName" placeholder="请输入发货人姓名" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="发货人电话" prop="senderMobile">
            <el-input v-model="formData.senderMobile" placeholder="请输入发货人电话" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="发货人地址" prop="senderAddress">
            <el-input v-model="formData.senderAddress" placeholder="请输入发货人地址" />
          </el-form-item>
        </el-col>
      </el-row>
      
      <!-- 收货人信息 -->
      <el-divider content-position="left">收货人信息</el-divider>
      <el-row>
        <el-col :span="8">
          <el-form-item label="收货人姓名" prop="receiverName">
            <el-input v-model="formData.receiverName" placeholder="请输入收货人姓名" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="收货人电话" prop="receiverMobile">
            <el-input v-model="formData.receiverMobile" placeholder="请输入收货人电话" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="收货人地址" prop="receiverAddress">
            <el-input v-model="formData.receiverAddress" placeholder="请输入收货人地址" />
          </el-form-item>
        </el-col>
      </el-row>
      
      <!-- 业务信息 -->
      <el-divider content-position="left">业务信息</el-divider>
      <el-row>
        <el-col :span="12">
          <el-form-item label="回单类型" prop="receiptType">
            <el-select v-model="formData.receiptType" placeholder="请选择回单类型">
              <el-option
                v-for="dict in getStrDictOptions(DICT_TYPE.JISHU_RECEIPT_TYPE)"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="结算类型" prop="paymentType">
            <el-select v-model="formData.paymentType" placeholder="请选择结算类型">
              <el-option
                v-for="dict in getStrDictOptions(DICT_TYPE.JISHU_PAYMENT_TYPE)"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="12">
          <el-form-item label="分发区域" prop="distributionArea">
            <el-select v-model="formData.distributionArea" placeholder="请选择分发区域">
              <el-option
                v-for="dict in getStrDictOptions(DICT_TYPE.JISHU_DISTRIBUTION_AREA)"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="目的地区域" prop="destinationArea">
            <el-input v-model="formData.destinationArea" placeholder="请输入目的地区域" />
          </el-form-item>
        </el-col>
      </el-row>
      
      <!-- 状态信息 -->
      <el-divider content-position="left">状态信息</el-divider>
      <el-row>
        <el-col :span="12">
          <el-form-item label="分发状态" prop="distributionStatus">
            <el-select v-model="formData.distributionStatus" placeholder="请选择分发状态">
              <el-option
                v-for="dict in getStrDictOptions(DICT_TYPE.JISHU_DISTRIBUTION_STATUS)"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="入库状态" prop="warehouseStatus">
            <el-select v-model="formData.warehouseStatus" placeholder="请选择入库状态">
              <el-option
                v-for="dict in getStrDictOptions(DICT_TYPE.JISHU_WAREHOUSE_STATUS)"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="12">
          <el-form-item label="配载状态" prop="loadingStatus">
            <el-select v-model="formData.loadingStatus" placeholder="请选择配载状态">
              <el-option
                v-for="dict in getStrDictOptions(DICT_TYPE.JISHU_LOADING_STATUS)"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="作业状态" prop="workStatus">
            <el-select v-model="formData.workStatus" placeholder="请选择作业状态">
              <el-option
                v-for="dict in getStrDictOptions(DICT_TYPE.JISHU_WORK_STATUS)"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      
      <el-form-item label="备注" prop="remark">
        <el-input
          v-model="formData.remark"
          type="textarea"
          placeholder="请输入备注"
          :rows="3"
        />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="submitForm" type="primary" :disabled="formLoading">确 定</el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template>
  </Dialog>
</template>

<script setup lang="ts">
import { ArrivalDetailApi, ArrivalDetailVO } from '@/api/jishu/arrivalDetail'
import { ArrivalApi, ArrivalVO } from '@/api/jishu/arrival'
import { DICT_TYPE, getStrDictOptions } from '@/utils/dict'

/** 货物明细 表单 */
defineOptions({ name: 'ArrivalDetailForm' })

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formType = ref('') // 表单的类型：create - 新增；update - 修改
const arrivalList = ref<ArrivalVO[]>([]) // 车次信息列表
const formData = ref({
  id: undefined,
  arrivalId: undefined,
  waybillNo: undefined,
  systemWaybillNo: undefined,
  quantity: undefined,
  weight: undefined,
  volume: undefined,
  cubicVolume: undefined,
  senderName: undefined,
  senderMobile: undefined,
  senderAddress: undefined,
  receiverName: undefined,
  receiverMobile: undefined,
  receiverAddress: undefined,
  goodsName: undefined,
  receiptType: 'electronic', // 默认电子回单
  paymentType: 'prepaid', // 默认现付
  distributionStatus: 'pending', // 默认待分发
  warehouseStatus: 'pending', // 默认待入库
  loadingStatus: 'pending', // 默认未配载
  workStatus: 'pending', // 默认待接单
  distributionArea: 'city', // 默认市内调度
  destinationArea: undefined,
  remark: undefined
})
const formRules = reactive({
  arrivalId: [{ required: true, message: '车次信息不能为空', trigger: 'change' }],
  waybillNo: [{ required: true, message: '运单号不能为空', trigger: 'blur' }],
  quantity: [{ required: true, message: '件数不能为空', trigger: 'blur' }],
  weight: [{ required: true, message: '重量不能为空', trigger: 'blur' }],
  volume: [{ required: true, message: '方数不能为空', trigger: 'blur' }],
  senderName: [{ required: true, message: '发货人姓名不能为空', trigger: 'blur' }],
  senderMobile: [{ required: true, message: '发货人电话不能为空', trigger: 'blur' }],
  senderAddress: [{ required: true, message: '发货人地址不能为空', trigger: 'blur' }],
  receiverName: [{ required: true, message: '收货人姓名不能为空', trigger: 'blur' }],
  receiverMobile: [{ required: true, message: '收货人电话不能为空', trigger: 'blur' }],
  receiverAddress: [{ required: true, message: '收货人地址不能为空', trigger: 'blur' }],
  goodsName: [{ required: true, message: '货物名称不能为空', trigger: 'blur' }],
  receiptType: [{ required: true, message: '回单类型不能为空', trigger: 'change' }],
  paymentType: [{ required: true, message: '结算类型不能为空', trigger: 'change' }]
})
const formRef = ref() // 表单 Ref

/** 打开弹窗 */
const open = async (type: string, id?: number) => {
  dialogVisible.value = true
  dialogTitle.value = t('action.' + type)
  formType.value = type
  resetForm()
  // 加载车次信息列表
  await loadArrivalList()
  // 修改时，设置数据
  if (id) {
    formLoading.value = true
    try {
      formData.value = await ArrivalDetailApi.getArrivalDetail(id)
    } finally {
      formLoading.value = false
    }
  }
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

/** 加载车次信息列表 */
const loadArrivalList = async () => {
  try {
    arrivalList.value = await ArrivalApi.getArrivalSimpleList()
  } catch (error) {
    console.error('加载车次信息列表失败:', error)
  }
}

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  await formRef.value.validate()
  // 提交请求
  formLoading.value = true
  try {
    const data = formData.value as unknown as ArrivalDetailVO
    if (formType.value === 'create') {
      await ArrivalDetailApi.createArrivalDetail(data)
      message.success(t('common.createSuccess'))
    } else {
      await ArrivalDetailApi.updateArrivalDetail(data)
      message.success(t('common.updateSuccess'))
    }
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    id: undefined,
    arrivalId: undefined,
    waybillNo: undefined,
    quantity: undefined,
    weight: undefined,
    volume: undefined,
    cubicVolume: undefined,
    senderName: undefined,
    senderMobile: undefined,
    senderAddress: undefined,
    receiverName: undefined,
    receiverMobile: undefined,
    receiverAddress: undefined,
    goodsName: undefined,
    receiptType: 'electronic', // 默认电子回单
    paymentType: 'prepaid', // 默认现付
    distributionStatus: 'pending', // 默认待分发
    warehouseStatus: 'pending', // 默认待入库
    loadingStatus: 'pending', // 默认未配载
    workStatus: 'pending', // 默认待接单
    distributionArea: 'city', // 默认市内调度
    destinationArea: undefined,
    remark: undefined
  }
  formRef.value?.resetFields()
}
</script>
